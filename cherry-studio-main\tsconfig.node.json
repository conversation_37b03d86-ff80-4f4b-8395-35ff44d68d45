{"extends": "@electron-toolkit/tsconfig/tsconfig.node.json", "include": ["electron.vite.config.*", "src/main/**/*", "src/preload/**/*", "src/main/env.d.ts", "src/renderer/src/types/*", "packages/shared/**/*", "scripts", "packages/mcp-trace/**/*", "src/renderer/src/services/traceApi.ts"], "compilerOptions": {"composite": true, "types": ["electron-vite/node", "vitest/globals"], "baseUrl": ".", "paths": {"@logger": ["src/main/services/LoggerService"], "@main/*": ["src/main/*"], "@types": ["src/renderer/src/types/index.ts"], "@shared/*": ["packages/shared/*"], "@mcp-trace/*": ["packages/mcp-trace/*"]}, "experimentalDecorators": true, "emitDecoratorMetadata": true, "useDefineForClassFields": true}}