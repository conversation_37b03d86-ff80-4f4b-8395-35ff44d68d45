{"extends": "@electron-toolkit/tsconfig/tsconfig.web.json", "include": ["src/renderer/src/**/*", "src/preload/*.d.ts", "local/src/renderer/**/*", "packages/shared/**/*", "tests/__mocks__/**/*", "packages/mcp-trace/**/*"], "compilerOptions": {"composite": true, "jsx": "react-jsx", "baseUrl": ".", "moduleResolution": "bundler", "paths": {"@logger": ["src/renderer/src/services/LoggerService"], "@renderer/*": ["src/renderer/src/*"], "@shared/*": ["packages/shared/*"], "@types": ["src/renderer/src/types/index.ts"], "@mcp-trace/*": ["packages/mcp-trace/*"]}, "experimentalDecorators": true, "emitDecoratorMetadata": true, "useDefineForClassFields": true}}